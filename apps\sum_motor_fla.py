#!/usr/bin/env python3
"""

Sum Motor FLA Script

This script loops through all devices with a designation starting with "-M",
adds up their FLA values, records the horsepower of the largest motor,
records the FLA of the motor with the largest FLA value,
and updates the project attributes with the total FLA, largest motor HP, and largest FLA.

Updated: 2025-01-08 - Migrated to use e3series PyPI package instead of win32com.client
"""

import os
import sys
import logging
import e3series
import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import theme utilities
try:
    from lib.theme_utils import apply_theme
except ImportError:
    # Define a basic theme utility if import fails
    def apply_theme(theme_name="red", appearance_mode="dark"):
        ctk.set_appearance_mode(appearance_mode)
        ctk.set_default_color_theme("blue")
        logging.info(f"Using fallback theme: {appearance_mode} mode with blue color theme")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sum_motor_fla.log'))
    ]
)

class SumMotorFLAApp:
    """Application to sum FLA values for motors and update project attribute."""

    def __init__(self, root):
        """Initialize the application."""
        self.root = root
        self.root.title("Sum Motor FLA")
        self.root.geometry("600x400")
        self.root.minsize(600, 400)

        # Apply theme
        apply_theme("red", "dark")

        # Create main frame
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Create title label
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Sum Motor FLA",
            font=("Arial", 20, "bold")
        )
        title_label.pack(pady=(0, 20))

        # Create description label
        desc_label = ctk.CTkLabel(
            self.main_frame,
            text="This tool will sum the FLA values for all devices with a designation starting with '-M',\n"
                 "record the horsepower of the largest motor, record the FLA of the motor with the largest FLA value,\n"
                 "and update the project attributes with the total FLA, largest motor HP, and largest FLA.",
            font=("Arial", 12),
            wraplength=550
        )
        desc_label.pack(pady=(0, 20))

        # Create status label
        self.status_var = tk.StringVar(value="Ready to process")
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            textvariable=self.status_var,
            font=("Arial", 12),
            wraplength=550
        )
        self.status_label.pack(pady=(0, 20))

        # Create result frame
        self.result_frame = ctk.CTkFrame(self.main_frame)
        self.result_frame.pack(fill="x", pady=(0, 20))

        # Create result label
        self.result_var = tk.StringVar(value="")
        self.result_label = ctk.CTkLabel(
            self.result_frame,
            textvariable=self.result_var,
            font=("Arial", 14, "bold"),
            wraplength=550
        )
        self.result_label.pack(pady=10)

        # Create button frame
        button_frame = ctk.CTkFrame(self.main_frame)
        button_frame.pack(fill="x", pady=(0, 20))

        # Create process button
        process_button = ctk.CTkButton(
            button_frame,
            text="Process Project",
            command=self.process_project,
            font=("Arial", 12, "bold")
        )
        process_button.pack(side="left", padx=10, pady=10)

        # Create exit button
        exit_button = ctk.CTkButton(
            button_frame,
            text="Exit",
            command=self.root.destroy,
            font=("Arial", 12, "bold")
        )
        exit_button.pack(side="right", padx=10, pady=10)

    def process_project(self):
        """Process the current project to sum FLA values for motors."""
        try:
            self.status_var.set("Connecting to application...")
            self.root.update()

            # Connect to the application
            app = e3series.Application()
            app.PutInfo(0, "Starting Motor FLA Summation...")

            # Create job and device objects
            job = app.CreateJobObject()
            device = job.CreateDeviceObject()

            # Get all device IDs
            self.status_var.set("Getting all devices...")
            self.root.update()
            device_ids = job.GetAllDeviceIds()

            # Log how many devices we found
            logging.info(f"Found {len(device_ids) if isinstance(device_ids, list) else 'unknown number of'} devices")

            # Initialize variables
            total_fla = 0.0
            motor_count = 0
            largest_hp = 0.0
            largest_hp_motor = ""
            largest_fla = 0.0
            largest_fla_motor = ""

            # Process each device
            self.status_var.set("Processing devices...")
            self.root.update()

            for dev in device_ids:
                if isinstance(dev, tuple):
                    for dev_id in dev:
                        if dev_id is None:
                            continue
                        try:
                            # Set the device ID
                            device.SetId(dev_id)

                            # Get the device designation
                            designation = device.GetName()

                            # Debug: Log every device to see what we're getting
                            logging.info(f"Processing device ID: {dev_id}, Designation: {designation}")

                            # Check if designation starts with "-M"
                            if (designation and designation.startswith("-M")) or (designation and designation.startswith("-PS")):
                                # Try multiple ways to get the FLA value
                                fla_str = None
                                hp_str = None

                                # Try different case variations and common attribute names for FLA
                                for attr_name in ["FLA", "fla", "Fla", "FullLoadAmps", "FULL_LOAD_AMPS"]:
                                    try:
                                        value = device.GetAttributeValue(attr_name)
                                        if value and value.strip():
                                            logging.info(f"Found value using attribute name '{attr_name}': {value}")
                                            fla_str = value
                                            break
                                    except:
                                        pass

                                # Try different case variations and common attribute names for horsepower
                                for attr_name in ["HP", "hp", "Hp", "HorsePower", "HORSE_POWER", "HORSEPOWER", "Motor_HP", "MOTOR_HP"]:
                                    try:
                                        value = device.GetAttributeValue(attr_name)
                                        if value and value.strip():
                                            logging.info(f"Found HP value using attribute name '{attr_name}': {value}")
                                            hp_str = value
                                            break
                                    except:
                                        pass

                                # Convert FLA to float and add to total
                                if fla_str and fla_str.strip():
                                    try:
                                        fla = float(fla_str)
                                        total_fla += fla
                                        motor_count += 1
                                        logging.info(f"Device {designation} (ID: {dev_id}) has FLA: {fla}")
                                        app.PutInfo(0, f"Device {designation} (ID: {dev_id}) has FLA: {fla}")

                                        # Update largest FLA if this motor has a higher FLA
                                        if fla > largest_fla:
                                            largest_fla = fla
                                            largest_fla_motor = designation
                                            logging.info(f"New largest FLA motor: {designation} with {fla} A")
                                            app.PutInfo(0, f"New largest FLA motor: {designation} with {fla} A")

                                        # Check for horsepower and update largest if needed
                                        if hp_str and hp_str.strip():
                                            try:
                                                hp = float(hp_str)
                                                logging.info(f"Device {designation} (ID: {dev_id}) has HP: {hp}")
                                                app.PutInfo(0, f"Device {designation} (ID: {dev_id}) has HP: {hp}")

                                                # Update largest HP if this motor has a higher HP
                                                if hp > largest_hp:
                                                    largest_hp = hp
                                                    largest_hp_motor = designation
                                                    logging.info(f"New largest motor: {designation} with {hp} HP")
                                                    app.PutInfo(0, f"New largest motor: {designation} with {hp} HP")
                                            except ValueError:
                                                logging.warning(f"Device {designation} (ID: {dev_id}) has invalid HP value: {hp_str}")
                                                app.PutInfo(0, f"Device {designation} (ID: {dev_id}) has invalid HP value: {hp_str}")
                                    except ValueError:
                                        logging.warning(f"Device {designation} (ID: {dev_id}) has invalid FLA value: {fla_str}")
                                        app.PutInfo(0, f"Device {designation} (ID: {dev_id}) has invalid FLA value: {fla_str}")
                                else:
                                    logging.warning(f"No FLA value found for motor {designation} (ID: {dev_id})")
                        except Exception as exc:
                            logging.error(f"Error processing device ID {dev_id}: {exc}")
                            app.PutInfo(0, f"Error processing device ID {dev_id}: {exc}")
                else:
                    # Log more details about the device info
                    logging.info(f"Device info (type: {type(dev)}): {dev}")

                    # Try to process single device if it's not a tuple
                    try:
                        dev_id = dev
                        if dev_id is not None:
                            # Set the device ID
                            device.SetId(dev_id)

                            # Get the device designation
                            designation = device.GetName()
                            logging.info(f"Single device ID: {dev_id}, Designation: {designation}")

                            # Check if designation starts with "-M"
                            if designation and designation.startswith("-M"):
                                # Try to get FLA value
                                fla_str = device.GetAttributeValue("FLA")
                                logging.info(f"Device {designation} FLA value: {fla_str}")

                                # Try to get HP value
                                hp_str = device.GetAttributeValue("HP")
                                logging.info(f"Device {designation} HP value: {hp_str}")

                                # Convert FLA to float and add to total
                                if fla_str and fla_str.strip():
                                    try:
                                        fla = float(fla_str)
                                        total_fla += fla
                                        motor_count += 1
                                        logging.info(f"Device {designation} (ID: {dev_id}) has FLA: {fla}")
                                        app.PutInfo(0, f"Device {designation} (ID: {dev_id}) has FLA: {fla}")

                                        # Update largest FLA if this motor has a higher FLA
                                        if fla > largest_fla:
                                            largest_fla = fla
                                            largest_fla_motor = designation
                                            logging.info(f"New largest FLA motor: {designation} with {fla} A")
                                            app.PutInfo(0, f"New largest FLA motor: {designation} with {fla} A")

                                        # Check for horsepower and update largest if needed
                                        if hp_str and hp_str.strip():
                                            try:
                                                hp = float(hp_str)
                                                logging.info(f"Device {designation} (ID: {dev_id}) has HP: {hp}")
                                                app.PutInfo(0, f"Device {designation} (ID: {dev_id}) has HP: {hp}")

                                                # Update largest HP if this motor has a higher HP
                                                if hp > largest_hp:
                                                    largest_hp = hp
                                                    largest_hp_motor = designation
                                                    logging.info(f"New largest motor: {designation} with {hp} HP")
                                                    app.PutInfo(0, f"New largest motor: {designation} with {hp} HP")
                                            except ValueError:
                                                logging.warning(f"Device {designation} (ID: {dev_id}) has invalid HP value: {hp_str}")
                                                app.PutInfo(0, f"Device {designation} (ID: {dev_id}) has invalid HP value: {hp_str}")
                                    except ValueError:
                                        logging.warning(f"Device {designation} (ID: {dev_id}) has invalid FLA value: {fla_str}")
                                        app.PutInfo(0, f"Device {designation} (ID: {dev_id}) has invalid FLA value: {fla_str}")
                    except Exception as exc:
                        logging.error(f"Error processing single device ID {dev}: {exc}")

            # Update the project attributes
            self.status_var.set("Updating project attributes...")
            self.root.update()

            # Round the total FLA to 2 decimal places
            total_fla = round(total_fla, 2)

            # Round the largest HP to 2 decimal places
            largest_hp = round(largest_hp, 2)

            # Round the largest FLA to 2 decimal places
            largest_fla = round(largest_fla, 2)

            # Update the project attributes
            job.SetAttributeValue("FLA", str(total_fla))
            job.SetAttributeValue("LARGEST_MOTOR", str(largest_hp))
            job.SetAttributeValue("LARGEST_LOAD", str(largest_fla))

            # Display the result
            result_message = f"Total FLA for {motor_count} motors: {total_fla} A"

            if largest_fla > 0:
                result_message += f"\nLargest FLA: {largest_fla_motor} with {largest_fla} A"

            if largest_hp > 0:
                result_message += f"\nLargest motor: {largest_hp_motor} with {largest_hp} HP"
            else:
                result_message += "\nNo horsepower data found for any motors"

            self.result_var.set(result_message)

            # Log the result
            logging.info(result_message)
            app.PutInfo(0, result_message)

            # Show success message
            self.status_var.set("Processing complete!")

            success_message = f"Successfully processed {motor_count} loads.\n\nTotal FLA: {total_fla} A"

            if largest_fla > 0:
                success_message += f"\n\nLargest FLA: {largest_fla_motor} with {largest_fla} A"

            if largest_hp > 0:
                success_message += f"\n\nLargest motor: {largest_hp_motor} with {largest_hp} HP"
            else:
                success_message += "\n\nNo horsepower data found for any motors"

            success_message += "\n\nThe project attributes 'FLA', 'LARGEST_MOTOR', and 'LARGEST_FLA' have been updated."

            messagebox.showinfo("Success", success_message)

        except Exception as e:
            error_message = f"An error occurred: {str(e)}"
            logging.error(error_message)
            self.status_var.set("Error occurred!")
            self.result_var.set(error_message)
            messagebox.showerror("Error", error_message)
        finally:
            # Clean up COM objects
            try:
                app = None
                job = None
                device = None
                device_ids = None
            except:
                pass

def main():
    """Run the application."""
    root = ctk.CTk()
    app = SumMotorFLAApp(root)
    root.mainloop()

if __name__ == "__main__":
    try:
        # Handle PyInstaller frozen environment
        if getattr(sys, 'frozen', False):
            # Running as compiled executable
            # Add the _MEIPASS directory to the PATH environment variable
            os.environ['PATH'] = sys._MEIPASS + os.pathsep + os.environ['PATH']

            # Change working directory to the executable directory
            # This ensures that relative paths work correctly
            os.chdir(os.path.dirname(sys.executable))

            # Set up logging to a file in the user's temp directory
            log_file = os.path.join(os.path.expanduser('~'), 'sum_motor_fla.log')
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler(log_file)
                ]
            )
            logging.info(f"Running from frozen environment: {sys.executable}")
    except Exception as e:
        # If there's an error in the PyInstaller-specific code, log it but continue
        print(f"Error setting up frozen environment: {str(e)}")

    # Run the main application
    main()
