import clr
import getpass
import sys

# 1) Load Vault SDK assemblies
clr.AddReference('Autodesk.DataManagement.Client.Framework')
clr.AddReference('Autodesk.DataManagement.Client.Framework.Vault.Currency')
clr.AddReference('Autodesk.Connectivity.WebServices')
clr.AddReference('Autodesk.Connectivity.WebServicesTools')

from Autodesk.DataManagement.Client.Framework.Connections import ConnectionManager, AuthType
from Autodesk.Connectivity.WebServices import PropValue, FilePropertyUpdateRequest
from Autodesk.Connectivity.WebServicesTools import DocumentService, FolderService

def read_password(prompt="Password: "):
    return getpass.getpass(prompt)

def gather_files_recursively(folder_svc, doc_svc, folder_id):
    """
    Returns a list of all latest-file WS_File objects under the given folder_id,
    including all subfolders.
    """
    files = list(doc_svc.GetLatestFilesByFolderId(folder_id))
    # get direct subfolders
    subfolders = folder_svc.GetFoldersByParentId(folder_id)
    for sf in subfolders:
        files.extend(gather_files_recursively(folder_svc, doc_svc, sf.Id))
    return files

def main():
    # — Vault connection info —
    server    = input("Vault server (e.g. myserver:8080): ").strip()
    vault     = input("Vault name: ").strip()
    username  = input("Username: ").strip()
    password  = read_password()

    # — Log in —
    try:
        conn = ConnectionManager.LogIn(
            vault, server, username, password, AuthType.Standard, None
        )
    except Exception as e:
        print("Login failed:", e)
        sys.exit(1)

    doc_svc    = conn.WebServiceManager.DocumentService
    folder_svc = conn.WebServiceManager.FolderService

    # — User inputs —
    folder_path  = input("Vault folder path (e.g. $/Projects/Widgets): ").strip()
    find_text    = input("String to find in Description: ")
    replace_text = input("String to replace with: ")

    # — Locate starting folder —
    ws_folder = folder_svc.FindFolderByPath(folder_path)
    if ws_folder is None:
        print(f"ERROR: Folder '{folder_path}' not found.")
        sys.exit(1)

    # — Gather all files under folder and its subfolders —
    all_files = gather_files_recursively(folder_svc, doc_svc, ws_folder.Id)
    print(f"Found {len(all_files)} files (including subfolders). Scanning...")

    # — Prepare updates —
    updates = []
    for f in all_files:
        desc_prop = next((p for p in f.PropertyValues if p.PropDefName == "Description"), None)
        if desc_prop is None:
            continue

        old_desc = desc_prop.Value or ""
        if find_text not in old_desc:
            continue

        new_desc = old_desc.replace(find_text, replace_text)
        if new_desc == old_desc:
            continue

        print(f"→ Updating {f.Name} (v{f.Iteration})")
        pv = PropValue()
        pv.propDefId = desc_prop.PropDefId
        pv.Value     = new_desc

        req = FilePropertyUpdateRequest()
        req.fileId        = f.Id
        req.latestVersion = True
        req.propValues    = [pv]

        updates.append(req)

    # — Commit updates in one batch —
    if updates:
        doc_svc.UpdateFileProperties(updates)
        print(f"Done: {len(updates)} file(s) updated.")
    else:
        print("No descriptions needed updating.")

if __name__ == "__main__":
    main()
